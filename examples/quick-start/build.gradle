plugins {
    id "application"
}

def guavaVersion = "33.4.8"
def failureAccessVersion = "1.0.3"
def listenableFutureVersion = "9999.0-empty-to-avoid-conflict-with-guava"
def jspecifyVersion = "1.0.0"
def errorProneVersion = "2.36.0"
def j2objcVersion = "3.0.0"

// Define all dependency jars
def guavaJar = file("libs/guava-${guavaVersion}-jre.jar")
def failureAccessJar = file("libs/failureaccess-${failureAccessVersion}.jar")
def listenableFutureJar = file("libs/listenablefuture-${listenableFutureVersion}.jar")
def jspecifyJar = file("libs/jspecify-${jspecifyVersion}.jar")
def errorProneJar = file("libs/error_prone_annotations-${errorProneVersion}.jar")
def j2objcJar = file("libs/j2objc-annotations-${j2objcVersion}.jar")

tasks.register("downloadGuava", Exec) {
    commandLine "curl", "--create-dirs", "-o", guavaJar.absolutePath,
            "https://repo1.maven.org/maven2/com/google/guava/guava/${guavaVersion}-jre/guava-${guavaVersion}-jre.jar"
    outputs.file guavaJar
}

tasks.register("downloadFailureAccess", Exec) {
    commandLine "curl", "--create-dirs", "-o", failureAccessJar.absolutePath,
            "https://repo1.maven.org/maven2/com/google/guava/failureaccess/${failureAccessVersion}/failureaccess-${failureAccessVersion}.jar"
    outputs.file failureAccessJar
}

tasks.register("downloadListenableFuture", Exec) {
    commandLine "curl", "--create-dirs", "-o", listenableFutureJar.absolutePath,
            "https://repo1.maven.org/maven2/com/google/guava/listenablefuture/${listenableFutureVersion}/listenablefuture-${listenableFutureVersion}.jar"
    outputs.file listenableFutureJar
}

tasks.register("downloadJspecify", Exec) {
    commandLine "curl", "--create-dirs", "-o", jspecifyJar.absolutePath,
            "https://repo1.maven.org/maven2/org/jspecify/jspecify/${jspecifyVersion}/jspecify-${jspecifyVersion}.jar"
    outputs.file jspecifyJar
}

tasks.register("downloadErrorProne", Exec) {
    commandLine "curl", "--create-dirs", "-o", errorProneJar.absolutePath,
            "https://repo1.maven.org/maven2/com/google/errorprone/error_prone_annotations/${errorProneVersion}/error_prone_annotations-${errorProneVersion}.jar"
    outputs.file errorProneJar
}

tasks.register("downloadJ2objc", Exec) {
    commandLine "curl", "--create-dirs", "-o", j2objcJar.absolutePath,
            "https://repo1.maven.org/maven2/com/google/j2objc/j2objc-annotations/${j2objcVersion}/j2objc-annotations-${j2objcVersion}.jar"
    outputs.file j2objcJar
}

if (java.nio.file.Path.of("${projectDir}/shrunk-libs").toFile().exists()) {
    dependencies {
        implementation files("shrunk-libs/guava-${guavaVersion}-jre.jar")
        implementation files("shrunk-libs/failureaccess-${failureAccessVersion}.jar")
        implementation files("shrunk-libs/listenablefuture-${listenableFutureVersion}.jar")
        implementation files("shrunk-libs/jspecify-${jspecifyVersion}.jar")
        implementation files("shrunk-libs/error_prone_annotations-${errorProneVersion}.jar")
        implementation files("shrunk-libs/j2objc-annotations-${j2objcVersion}.jar")
    }
} else {
    dependencies {
        implementation files(guavaJar).builtBy(tasks.named("downloadGuava"))
        implementation files(failureAccessJar).builtBy(tasks.named("downloadFailureAccess"))
        implementation files(listenableFutureJar).builtBy(tasks.named("downloadListenableFuture"))
        implementation files(jspecifyJar).builtBy(tasks.named("downloadJspecify"))
        implementation files(errorProneJar).builtBy(tasks.named("downloadErrorProne"))
        implementation files(j2objcJar).builtBy(tasks.named("downloadJ2objc"))
    }
}

application {
    mainClass = "com.example.QuickStart"
}
